<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../admin/config.php';
} catch (Exception $e) {
    die("Config error: " . $e->getMessage());
}

// Get database connection
try {
    $pdo = getConnection();
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Create videos table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS `videos` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `description` text,
        `content` longtext,
        `youtube_url` varchar(500) DEFAULT NULL,
        `youtube_id` varchar(20) DEFAULT NULL,
        `video_base64` LONGBLOB DEFAULT NULL,
        `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube',
        `video_size` BIGINT DEFAULT NULL,
        `video_format` VARCHAR(10) DEFAULT NULL,
        `thumbnail` varchar(255) DEFAULT NULL,
        `category` varchar(100) DEFAULT 'Umum',
        `tags` text,
        `duration` varchar(10) DEFAULT '00:00',
        `status` enum('draft','published') DEFAULT 'published',
        `views` int(11) DEFAULT 0,
        `likes` int(11) DEFAULT 0,
        `shares` int(11) DEFAULT 0,
        `comments_count` int(11) DEFAULT 0,
        `featured` tinyint(1) DEFAULT 0,
        `created_by` int(11) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `category` (`category`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`),
        KEY `video_type` (`video_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
} catch (Exception $e) {
    error_log("Error creating videos table: " . $e->getMessage());
}

// Get published videos
try {
    $stmt = $pdo->prepare("
        SELECT
            id, title, description, content, youtube_url, youtube_id,
            video_base64, video_type, video_size, video_format,
            thumbnail, category, tags, duration, status, views, likes,
            shares, comments_count, featured, created_at, updated_at
        FROM videos
        WHERE status = 'published'
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check if videos table is empty and add sample data
    if (empty($videos)) {
        // Insert sample videos into database
        $sampleVideos = [
            [
                'title' => 'Breaking News: Teknologi AI Terbaru',
                'description' => 'Perkembangan teknologi AI yang mengubah dunia digital saat ini',
                'content' => 'Video berita eksklusif tentang perkembangan teknologi AI terbaru yang akan mengubah cara kita bekerja dan berinteraksi dengan teknologi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'youtube_id' => 'dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'category' => 'Teknologi',
                'tags' => 'teknologi,ai,digital,inovasi,berita',
                'duration' => '02:45'
            ],
            [
                'title' => 'Update Ekonomi Indonesia Terkini',
                'description' => 'Analisis mendalam kondisi ekonomi Indonesia dan proyeksi masa depan',
                'content' => 'Laporan komprehensif tentang perkembangan ekonomi Indonesia terbaru dengan analisis dari para ahli ekonomi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=M7lc1UVf-VE',
                'youtube_id' => 'M7lc1UVf-VE',
                'video_type' => 'youtube',
                'category' => 'Ekonomi',
                'tags' => 'ekonomi,indonesia,bisnis,keuangan',
                'duration' => '03:20'
            ],
            [
                'title' => 'Highlight Olahraga Hari Ini',
                'description' => 'Momen-momen terbaik dari berbagai pertandingan olahraga',
                'content' => 'Kumpulan highlight dan momen spektakuler dari dunia olahraga yang tidak boleh dilewatkan.',
                'youtube_url' => 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                'youtube_id' => '9bZkp7q19f0',
                'video_type' => 'youtube',
                'category' => 'Olahraga',
                'tags' => 'olahraga,sepakbola,highlight,kompetisi',
                'duration' => '04:15'
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO videos (
                title, description, content, youtube_url, youtube_id,
                video_type, category, tags, duration, status,
                views, likes, shares, comments_count, featured,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?, ?, ?, ?, 0, NOW(), NOW())
        ");

        foreach ($sampleVideos as $sample) {
            $insertStmt->execute([
                $sample['title'],
                $sample['description'],
                $sample['content'],
                $sample['youtube_url'],
                $sample['youtube_id'],
                $sample['video_type'],
                $sample['category'],
                $sample['tags'],
                $sample['duration'],
                rand(100, 1000), // views
                rand(10, 100),   // likes
                rand(5, 50),     // shares
                rand(0, 20)      // comments_count
            ]);
        }

        // Fetch the newly inserted videos
        $stmt->execute();
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    error_log("Database query error: " . $e->getMessage());
    // Fallback to sample data if database fails
    $videos = [[
        'id' => 1,
        'title' => 'Sample Video - Database Error',
        'description' => 'This is a fallback sample video due to database issues',
        'content' => 'Sample content',
        'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'youtube_id' => 'dQw4w9WgXcQ',
        'video_base64' => null,
        'video_type' => 'youtube',
        'video_size' => null,
        'video_format' => null,
        'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        'category' => 'Sample',
        'tags' => 'sample,test',
        'duration' => '03:32',
        'status' => 'published',
        'views' => 100,
        'likes' => 10,
        'shares' => 5,
        'comments_count' => 2,
        'featured' => 0,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]];
}

// Extract YouTube ID from URL
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : null; // Return null if no valid ID found
}

// Generate YouTube embed URL with sound enabled
function getYouTubeEmbedUrl($videoId, $autoplay = false) {
    if (empty($videoId)) {
        error_log("Warning: Empty video ID provided to getYouTubeEmbedUrl");
        return null;
    }

    $params = http_build_query([
        'autoplay' => $autoplay ? '1' : '0',
        'mute' => '0', // Enable sound
        'controls' => '1',
        'loop' => '1',
        'playlist' => $videoId,
        'rel' => '0',
        'showinfo' => '0',
        'modestbranding' => '1',
        'iv_load_policy' => '3',
        'fs' => '1',
        'disablekb' => '0',
        'enablejsapi' => '1',
        'origin' => 'http://localhost'
    ]);

    $embedUrl = "https://www.youtube.com/embed/{$videoId}?{$params}";
    error_log("Generated YouTube embed URL: " . $embedUrl);
    return $embedUrl;
}

// Get video source based on type
function getVideoSource($video) {
    $video_type = $video['video_type'] ?? 'youtube';

    error_log("Processing video: " . $video['title'] . " (Type: $video_type)");

    if ($video_type === 'upload' && !empty($video['video_base64'])) {
        // Create data URL from base64 video data
        $video_format = $video['video_format'] ?? 'mp4';
        $mimeType = 'video/' . $video_format;
        $dataUrl = 'data:' . $mimeType . ';base64,' . $video['video_base64'];

        return [
            'type' => 'upload',
            'src' => $dataUrl,
            'format' => $video_format,
            'size' => $video['video_size'] ?? 0
        ];
    } else {
        // Handle YouTube videos
        $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);

        if (empty($youtubeId)) {
            error_log("Warning: No valid YouTube ID found for video: " . $video['title']);
            error_log("YouTube URL: " . ($video['youtube_url'] ?? 'empty'));
            error_log("YouTube ID: " . ($video['youtube_id'] ?? 'empty'));
        }

        $embedUrl = getYouTubeEmbedUrl($youtubeId, false);

        return [
            'type' => 'youtube',
            'src' => $embedUrl,
            'youtube_id' => $youtubeId,
            'original_url' => $video['youtube_url'] ?? ''
        ];
    }
}

// Format file size
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video - News App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Debug information -->
    <script>
        console.log('🎬 Video page loaded');
        console.log('📊 Total videos found: <?= count($videos) ?>');
        <?php if (!empty($videos)): ?>
        console.log('🎥 First video:', <?= json_encode($videos[0]) ?>);
        <?php endif; ?>
    </script>

    <!-- Debug information -->
    <script>
        console.log('🎬 Video page loaded');
        console.log('📊 Total videos found: <?= count($videos) ?>');
        <?php if (!empty($videos)): ?>
        console.log('🎥 First video:', <?= json_encode($videos[0]) ?>);
        <?php endif; ?>
    </script>
    <style>
        .video-container {
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            width: 100%;
        }

        /* Play button styling - ukuran sedang */
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .play-button button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button button:hover {
            background: rgba(0, 0, 0, 0.9);
            border-color: white;
            transform: scale(1.1);
        }

        .play-button i {
            font-size: 18px;
            margin-left: 2px;
        }

        .video-playing .play-button {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
        }

        .video-paused .play-button {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .video-container {
                border-radius: 8px;
                height: 100vh;
                width: 100vw;
            }

            .play-button button {
                width: 50px;
                height: 50px;
            }

            .play-button i {
                font-size: 16px;
            }
        }

        @media (min-width: 769px) {
            .video-container {
                max-width: 800px;
                margin: 0 auto;
                aspect-ratio: 16/9;
            }
        }

        .video-scroll {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh;
        }
        .video-item {
            scroll-snap-align: start;
            height: 100vh;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Desktop: Center video with max width, hide bottom nav */
        @media (min-width: 769px) {
            .video-container {
                max-width: 450px;
                height: 80vh;
            }
            .bottom-nav {
                display: none;
            }
            .desktop-actions {
                right: -80px;
                top: 50%;
                transform: translateY(-50%);
                bottom: auto;
            }
            .desktop-info {
                bottom: 20px;
                right: -400px;
                width: 350px;
                left: auto;
            }
        }

        /* Mobile: Full screen video */
        @media (max-width: 768px) {
            .video-container {
                width: 100vw;
                height: 100vh;
                max-width: none;
                aspect-ratio: unset;
            }
            .desktop-actions {
                right: 16px;
                bottom: 128px;
                top: auto;
                transform: none;
            }
            .desktop-info {
                bottom: 80px;
                left: 16px;
                right: 80px;
                width: auto;
            }
        }

        /* Animations */
        .like-animation {
            animation: likeScale 0.3s ease-in-out;
        }

        .save-animation {
            animation: saveRotate 0.4s ease-in-out;
        }

        @keyframes likeScale {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }

        @keyframes saveRotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(10deg) scale(1.1); }
            100% { transform: rotate(0deg) scale(1); }
        }

        .action-btn {
            transition: all 0.2s ease-in-out;
        }

        .action-btn:hover {
            transform: scale(1.1);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .like-animation {
            animation: likeAnimation 0.3s ease-in-out;
        }

        @keyframes likeAnimation {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .save-animation {
            animation: saveAnimation 0.4s ease-in-out;
        }

        @keyframes saveAnimation {
            0% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(-5deg); }
            50% { transform: scale(1.2) rotate(5deg); }
            75% { transform: scale(1.1) rotate(-2deg); }
            100% { transform: scale(1) rotate(0deg); }
        }

        /* Desktop Action Button States */
        .like-btn.liked i {
            color: #ef4444 !important;
        }

        .save-btn.saved i {
            color: #eab308 !important;
        }

        /* Hover effects for desktop buttons */
        .like-btn:hover {
            background-color: rgba(239, 68, 68, 0.1) !important;
        }

        .save-btn:hover {
            background-color: rgba(234, 179, 8, 0.1) !important;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        /* Comment Modal */
        .comment-modal {
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
        }

        .comment-modal.show {
            transform: translateY(0);
        }

        /* Play Button */
        .play-button {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
            transform: scale(1);
            transition: all 0.3s ease-in-out;
            z-index: 30;
        }

        .play-button button {
            z-index: 31;
        }

        .video-paused .play-button {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
            transform: scale(1);
        }

        .video-playing .play-button {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transform: scale(0.8);
        }

        .play-button button:hover {
            transform: scale(1.1);
        }

        /* Hide play button for HTML5 videos with controls */
        .video-container[data-video-type="upload"] .play-button {
            display: none !important;
        }

        /* Ensure mobile HTML5 videos don't have play button overlay */
        .md\:hidden .video-container[data-video-type="upload"] .play-button {
            display: none !important;
        }

        /* HTML5 Video Styling */
        .video-container video {
            background: #000;
        }

        .video-container video::-webkit-media-controls-panel {
            background-color: rgba(0, 0, 0, 0.8);
        }

        /* Like Button States */
        .like-btn {
            transition: all 0.3s ease;
        }

        .like-btn.liked {
            background-color: rgba(239, 68, 68, 0.2) !important;
            border-color: rgba(239, 68, 68, 0.5) !important;
        }

        .like-btn.liked i {
            color: #ef4444 !important;
            animation: heartBeat 0.6s ease-in-out;
        }

        .like-animation {
            animation: likeAnimation 0.3s ease-in-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1); }
            75% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes likeAnimation {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Save Button States */
        .save-btn {
            transition: all 0.3s ease;
        }

        .save-btn.saved {
            background-color: rgba(251, 191, 36, 0.2) !important;
            border-color: rgba(251, 191, 36, 0.5) !important;
        }

        .save-btn.saved i {
            color: #fbbf24 !important;
        }

        .save-animation {
            animation: saveAnimation 0.3s ease-in-out;
        }

        @keyframes saveAnimation {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Mobile Action Buttons */
        .md\:hidden .action-btn {
            position: relative;
            z-index: 50;
            pointer-events: auto;
        }

        .md\:hidden .like-btn.liked {
            background-color: rgba(239, 68, 68, 0.3) !important;
            border: 2px solid rgba(239, 68, 68, 0.6) !important;
        }

        .md\:hidden .save-btn.saved {
            background-color: rgba(251, 191, 36, 0.3) !important;
            border: 2px solid rgba(251, 191, 36, 0.6) !important;
        }

        /* Ensure mobile buttons are clickable */
        @media (max-width: 768px) {
            .action-btn {
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
            }

            /* Mobile video specific styles */
            .video-container {
                height: 100vh;
                width: 100vw;
            }

            .video-container video {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain;
                background: #000;
            }

            .video-container iframe {
                width: 100% !important;
                height: 100% !important;
            }

            /* Ensure video is properly sized */
            video::-webkit-media-controls-panel {
                background-color: rgba(0, 0, 0, 0.8);
            }

            video::-webkit-media-controls-play-button {
                background-color: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
            }
        }

        /* Desktop specific styles */
        @media (min-width: 769px) {
            .video-item {
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .desktop-wrapper {
                display: flex;
                max-width: 1200px;
                width: 100%;
                gap: 40px;
                align-items: flex-start;
            }




        }
    </style>
</head>
<body class="bg-black text-white overflow-hidden">
    <!-- Debug info -->
    <div id="debug-info" class="fixed top-0 left-0 z-50 bg-red-600 text-white p-2 text-xs" style="display: none;">
        Videos loaded: <?= count($videos) ?> | Status: <?= !empty($videos) ? 'Has videos' : 'No videos' ?>
    </div>

    <div class="video-scroll" id="videoContainer">
        <?php if (empty($videos)): ?>
            <!-- Empty State -->
            <div class="video-item flex flex-col items-center justify-center h-screen">
                <div class="text-center">
                    <i class="fas fa-video text-8xl text-gray-500 mb-6"></i>
                    <h2 class="text-3xl font-bold mb-4 text-white">Maaf, Saat Ini Belum Ada Video</h2>
                    <p class="text-gray-400 text-lg mb-6 max-w-md mx-auto">
                        Video menarik akan segera hadir! Silakan kembali lagi nanti untuk menikmati konten video terbaru dari kami.
                    </p>
                    <div class="space-y-4">
                        <a href="http://localhost:3000" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors">
                            <i class="fas fa-home mr-2"></i>
                            Kembali ke Beranda
                        </a>
                        <br>
                        <a href="test-video-db.php" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-colors">
                            <i class="fas fa-database mr-2"></i>
                            Test Database
                        </a>
                        <br>
                        <a href="video-simple.php" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors">
                            <i class="fas fa-play mr-2"></i>
                            Simple Video Test
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Debug: Show that we have videos -->
            <script>
                console.log('✅ Videos found, rendering video player...');
                console.log('📊 Video count:', <?= count($videos) ?>);
                // Show debug info for 5 seconds
                if (document.getElementById('debug-info')) {
                    document.getElementById('debug-info').style.display = 'block';
                    setTimeout(() => {
                        document.getElementById('debug-info').style.display = 'none';
                    }, 5000);
                }
            </script>
            <?php foreach ($videos as $index => $video): ?>
                <?php
                try {
                    $videoSource = getVideoSource($video);
                    $video_type = $videoSource['type'];
                } catch (Exception $e) {
                    error_log("Error processing video: " . $e->getMessage());
                    continue; // Skip this video if there's an error
                }
                ?>
                <div class="video-item relative bg-black flex items-center justify-center" data-video-id="<?= $video['id'] ?>">
                    <!-- Search Icon (Top Right) -->
                    <div class="absolute top-6 right-6 z-50">
                        <button class="w-10 h-10 rounded-full bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-30 transition-all duration-200">
                            <i class="fas fa-search text-white text-lg"></i>
                        </button>
                    </div>

                    <!-- Desktop Layout (Responsive) -->
                    <div class="hidden md:flex w-full justify-center items-center min-h-screen p-4">
                        <div class="flex flex-col lg:flex-row gap-4 lg:gap-6 max-w-6xl w-full">
                            <!-- Video Container -->
                            <div class="flex-shrink-0 w-full lg:w-auto">
                                <div class="video-container relative bg-black rounded-lg overflow-hidden shadow-2xl video-paused mx-auto"
                                     style="width: 100%; max-width: 500px; aspect-ratio: 16/9;"
                                     data-video-id="<?= $video['id'] ?>" data-video-type="<?= $video_type ?>">
                                    <?php if ($video_type === 'upload'): ?>
                                        <!-- HTML5 Video Player -->
                                        <video
                                            class="w-full h-full object-cover"
                                            controls
                                            preload="metadata"
                                            id="video-<?= $video['id'] ?>"
                                            data-video-id="<?= $video['id'] ?>"
                                            onloadedmetadata="console.log('Video loaded: <?= $video['id'] ?>')"
                                            onerror="handleVideoError(this, <?= $video['id'] ?>)"
                                        >
                                            <source src="<?= htmlspecialchars($videoSource['src']) ?>" type="video/<?= htmlspecialchars($videoSource['format']) ?>">
                                            <p class="text-white text-center p-4">Browser Anda tidak mendukung video HTML5.</p>
                                        </video>

                                        <!-- Video Error Placeholder -->
                                        <div id="video-error-<?= $video['id'] ?>" class="hidden absolute inset-0 bg-gray-800 flex items-center justify-center">
                                            <div class="text-center text-white p-6">
                                                <i class="fas fa-exclamation-triangle text-4xl mb-4 text-yellow-500"></i>
                                                <h3 class="text-lg font-semibold mb-2">Video Tidak Tersedia</h3>
                                                <p class="text-sm text-gray-300 mb-4">File video tidak dapat dimuat</p>
                                                <p class="text-xs text-gray-400">Format: <?= strtoupper($videoSource['format']) ?></p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- YouTube Iframe -->
                                        <iframe
                                            src="<?= htmlspecialchars($videoSource['src']) ?>"
                                            class="w-full h-full border-0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                            allowfullscreen
                                            title="<?= htmlspecialchars($video['title']) ?>"
                                            onload="console.log('✅ Desktop video loaded: <?= $videoSource['youtube_id'] ?? 'unknown' ?>')"
                                            onerror="console.error('❌ Desktop video failed to load: <?= $videoSource['youtube_id'] ?? 'unknown' ?>')"
                                            id="video-<?= $video['id'] ?>"
                                            frameborder="0"
                                        ></iframe>

                                        <!-- Fallback for iframe loading issues -->
                                        <div id="iframe-fallback-<?= $video['id'] ?>" class="hidden absolute inset-0 bg-gray-800 flex items-center justify-center">
                                            <div class="text-center text-white p-6">
                                                <i class="fas fa-play-circle text-6xl mb-4 text-red-500"></i>
                                                <h3 class="text-lg font-semibold mb-2">Video YouTube</h3>
                                                <p class="text-sm text-gray-300 mb-4"><?= htmlspecialchars($video['title']) ?></p>
                                                <a href="<?= htmlspecialchars($video['youtube_url']) ?>" target="_blank"
                                                   class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                                    <i class="fab fa-youtube mr-2"></i>
                                                    Tonton di YouTube
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Play Button Overlay -->
                                    <div class="play-button absolute inset-0 flex items-center justify-center z-30">
                                        <button
                                            onclick="toggleVideoPlay(<?= $video['id'] ?>)"
                                            class="w-16 h-16 lg:w-20 lg:h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center shadow-lg transition-colors cursor-pointer z-40"
                                        >
                                            <i class="fas fa-play text-white text-xl lg:text-2xl ml-1"></i>
                                        </button>
                                    </div>

                                    <!-- Video Overlay for Interaction -->
                                    <div class="absolute inset-0 z-10 cursor-pointer" onclick="toggleVideoPlay(<?= $video['id'] ?>)"></div>
                                </div>
                            </div>

                            <!-- Right Side Actions (TikTok Style) -->
                            <div class="flex flex-col items-center space-y-4 pt-6">
                                <!-- Profile -->
                                <div class="flex flex-col items-center relative">
                                    <div class="w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border-2 border-white relative">
                                        <i class="fas fa-user text-white text-base lg:text-lg"></i>
                                        <!-- Plus button positioned at top-right -->
                                        <div class="absolute -top-1 -right-1 w-5 h-5 lg:w-6 lg:h-6 rounded-full bg-red-500 flex items-center justify-center border-2 border-white">
                                            <i class="fas fa-plus text-white text-xs"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- Like Button -->
                                <div class="flex flex-col items-center">
                                    <button
                                        onclick="toggleLike(<?= $video['id'] ?>)"
                                        class="like-btn action-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-all duration-300 border border-gray-600 mb-1"
                                        data-video-id="<?= $video['id'] ?>"
                                    >
                                        <i class="fas fa-heart text-white text-lg lg:text-xl"></i>
                                    </button>
                                    <span class="text-white text-xs lg:text-sm font-semibold like-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['likes']) ?></span>
                                </div>

                                <!-- Comment Button -->
                                <div class="flex flex-col items-center">
                                    <button
                                        onclick="openCommentModal(<?= $video['id'] ?>)"
                                        class="action-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-all duration-300 border border-gray-600 mb-1"
                                    >
                                        <i class="fas fa-comment-dots text-white text-lg lg:text-xl"></i>
                                    </button>
                                    <span class="text-white text-xs lg:text-sm font-semibold"><?= number_format($video['comments_count']) ?></span>
                                </div>

                                <!-- Save Button -->
                                <div class="flex flex-col items-center">
                                    <button
                                        onclick="toggleSave(<?= $video['id'] ?>)"
                                        class="save-btn action-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-all duration-300 border border-gray-600 mb-1"
                                        data-video-id="<?= $video['id'] ?>"
                                    >
                                        <i class="fas fa-bookmark text-white text-lg lg:text-xl"></i>
                                    </button>
                                    <span class="text-white text-xs lg:text-sm font-semibold">Simpan</span>
                                </div>

                                <!-- Share Button -->
                                <div class="flex flex-col items-center">
                                    <button
                                        onclick="shareVideo(<?= $video['id'] ?>)"
                                        class="action-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-all duration-300 border border-gray-600 mb-1"
                                        data-video-id="<?= $video['id'] ?>"
                                    >
                                        <i class="fas fa-share-alt text-white text-lg lg:text-xl"></i>
                                    </button>
                                    <span class="text-white text-xs lg:text-sm font-semibold share-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['shares']) ?></span>
                                </div>
                            </div>

                            <!-- Video Info Panel (Right Side) -->
                            <div class="w-80 space-y-4">
                                <!-- Title -->
                                <h1 class="text-white text-xl font-bold leading-tight">
                                    <?= htmlspecialchars($video['title']) ?>
                                </h1>

                                <!-- Channel Info -->
                                <div class="flex items-center space-x-3 p-4 bg-gray-900 rounded-lg">
                                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                                        <i class="fas fa-user text-white text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-white font-semibold">@<?= htmlspecialchars($video['username'] ?? 'admin') ?></h3>
                                        <p class="text-gray-400 text-sm"><?= number_format($video['views']) ?> tayangan</p>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="p-4 bg-gray-900 rounded-lg">
                                    <p class="text-gray-300 text-sm leading-relaxed mb-4">
                                        <?= htmlspecialchars($video['description'] ?: $video['content']) ?>
                                    </p>

                                    <!-- Stats -->
                                    <div class="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                                        <span><i class="fas fa-calendar mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                                        <span><i class="fas fa-clock mr-1"></i><?= date('H:i', strtotime($video['created_at'])) ?></span>
                                        <?php if ($video_type === 'upload'): ?>
                                            <span><i class="fas fa-hdd mr-1"></i><?= formatFileSize($videoSource['size']) ?></span>
                                            <span><i class="fas fa-file-video mr-1"></i><?= strtoupper($videoSource['format']) ?></span>
                                        <?php else: ?>
                                            <span><i class="fab fa-youtube mr-1"></i>YouTube</span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Tags -->
                                    <?php if (!empty($video['tags'])): ?>
                                        <div class="flex flex-wrap gap-2">
                                            <?php
                                            $tags = explode(',', $video['tags']);
                                            foreach ($tags as $tag):
                                                $tag = trim($tag);
                                                if (!empty($tag)):
                                            ?>
                                                <span class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-full text-sm cursor-pointer transition-colors">
                                                    #<?= htmlspecialchars($tag) ?>
                                                </span>
                                            <?php
                                                endif;
                                            endforeach;
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Related Videos -->
                                <div class="bg-gray-900 rounded-lg p-4">
                                    <h3 class="text-white font-semibold mb-4">Related Videos</h3>
                                    <div class="space-y-3">
                                        <div class="text-gray-400 text-sm text-center py-8">
                                            Related videos will appear here
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Layout -->
                    <div class="md:hidden w-full mx-auto video-container relative bg-gray-900 video-paused h-screen" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $video_type ?>">
                        <?php if ($video_type === 'upload'): ?>
                            <!-- HTML5 Video Player -->
                            <video
                                class="w-full h-full object-contain bg-black"
                                controls
                                preload="metadata"
                                playsinline
                                webkit-playsinline
                                id="video-mobile-<?= $video['id'] ?>"
                                data-video-id="<?= $video['id'] ?>"
                                onloadedmetadata="console.log('Mobile video loaded: <?= $video['id'] ?>')"
                                onerror="handleVideoError(this, <?= $video['id'] ?>)"
                                onclick="handleMobileVideoClick(this, <?= $video['id'] ?>)"
                            >
                                <source src="<?= htmlspecialchars($videoSource['src']) ?>" type="video/<?= htmlspecialchars($videoSource['format']) ?>">
                                <p class="text-white text-center p-4">Browser Anda tidak mendukung video HTML5.</p>
                            </video>

                            <!-- Mobile Video Error Placeholder -->
                            <div id="video-error-mobile-<?= $video['id'] ?>" class="hidden absolute inset-0 bg-gray-800 flex items-center justify-center">
                                <div class="text-center text-white p-4">
                                    <i class="fas fa-exclamation-triangle text-3xl mb-3 text-yellow-500"></i>
                                    <h3 class="text-base font-semibold mb-2">Video Tidak Tersedia</h3>
                                    <p class="text-xs text-gray-300 mb-2">File video tidak dapat dimuat</p>
                                    <p class="text-xs text-gray-400">Format: <?= strtoupper($videoSource['format']) ?></p>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- YouTube Iframe -->
                            <iframe
                                src="<?= htmlspecialchars($videoSource['src']) ?>"
                                class="w-full h-full border-0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                allowfullscreen
                                title="<?= htmlspecialchars($video['title']) ?>"
                                onload="console.log('✅ Mobile video loaded: <?= $videoSource['youtube_id'] ?? 'unknown' ?>')"
                                onerror="console.error('❌ Mobile video failed to load: <?= $videoSource['youtube_id'] ?? 'unknown' ?>')"
                                id="video-mobile-<?= $video['id'] ?>"
                                frameborder="0"
                            ></iframe>

                            <!-- Mobile Fallback for iframe loading issues -->
                            <div id="mobile-iframe-fallback-<?= $video['id'] ?>" class="hidden absolute inset-0 bg-gray-800 flex items-center justify-center">
                                <div class="text-center text-white p-4">
                                    <i class="fas fa-play-circle text-4xl mb-3 text-red-500"></i>
                                    <h3 class="text-base font-semibold mb-2">Video YouTube</h3>
                                    <p class="text-xs text-gray-300 mb-3"><?= htmlspecialchars($video['title']) ?></p>
                                    <a href="<?= htmlspecialchars($video['youtube_url']) ?>" target="_blank"
                                       class="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors">
                                        <i class="fab fa-youtube mr-2"></i>
                                        Tonton di YouTube
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Play Button Overlay -->
                        <div class="play-button absolute inset-0 flex items-center justify-center z-30">
                            <button
                                onclick="toggleVideoPlay(<?= $video['id'] ?>)"
                                class="w-16 h-16 sm:w-20 sm:h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 cursor-pointer z-40"
                            >
                                <i class="fas fa-play text-white text-xl sm:text-2xl ml-1"></i>
                            </button>
                        </div>

                        <!-- Video Overlay for Interaction -->
                        <div class="absolute inset-0 z-10 cursor-pointer" onclick="toggleVideoPlay(<?= $video['id'] ?>)"></div>





                        <!-- Mobile Right Side Actions (TikTok Style) -->
                        <div class="md:hidden absolute right-3 bottom-28 flex flex-col items-center space-y-3 z-50">
                            <!-- Profile -->
                            <div class="flex flex-col items-center relative">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border-2 border-white relative">
                                    <i class="fas fa-user text-white text-base"></i>
                                    <!-- Plus button positioned at top-right -->
                                    <div class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center border-2 border-white">
                                        <i class="fas fa-plus text-white text-xs"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Like Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleLike(<?= $video['id'] ?>); event.stopPropagation();"
                                    class="like-btn action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                                    data-video-id="<?= $video['id'] ?>"
                                    style="pointer-events: auto;"
                                >
                                    <i class="fas fa-heart text-white text-lg pointer-events-none"></i>
                                </button>
                                <span class="text-white text-xs font-semibold like-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['likes']) ?></span>
                            </div>

                            <!-- Comment Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="openCommentModal(<?= $video['id'] ?>); event.stopPropagation();"
                                    class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                                    style="pointer-events: auto;"
                                >
                                    <i class="fas fa-comment-dots text-white text-lg pointer-events-none"></i>
                                </button>
                                <span class="text-white text-xs font-semibold"><?= number_format($video['comments_count']) ?></span>
                            </div>

                            <!-- Share Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="shareVideo(<?= $video['id'] ?>); event.stopPropagation();"
                                    class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                                    data-video-id="<?= $video['id'] ?>"
                                    style="pointer-events: auto;"
                                >
                                    <i class="fas fa-share-alt text-white text-lg pointer-events-none"></i>
                                </button>
                                <span class="text-white text-xs font-semibold share-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['shares']) ?></span>
                            </div>

                            <!-- Save Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleSave(<?= $video['id'] ?>); event.stopPropagation();"
                                    class="save-btn action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                                    data-video-id="<?= $video['id'] ?>"
                                    style="pointer-events: auto;"
                                >
                                    <i class="fas fa-bookmark text-white text-lg pointer-events-none"></i>
                                </button>
                                <span class="text-white text-xs font-semibold">Simpan</span>
                            </div>
                        </div>



                        <!-- Mobile Bottom Info -->
                        <div class="md:hidden absolute bottom-24 left-4 right-20 z-20">
                            <div class="text-white">
                                <h2 class="text-lg font-bold mb-2 line-clamp-2"><?= htmlspecialchars($video['title']) ?></h2>
                                <p class="text-sm opacity-80 mb-2 line-clamp-2"><?= htmlspecialchars($video['description'] ?: $video['content']) ?></p>
                                <div class="flex items-center space-x-4 text-xs opacity-70">
                                    <span><i class="fas fa-eye mr-1"></i><?= number_format($video['views']) ?></span>
                                    <span><i class="fas fa-calendar mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                                    <?php if ($video_type === 'upload' && !empty($videoSource['size'])): ?>
                                        <span><i class="fas fa-hdd mr-1"></i><?= formatFileSize($videoSource['size']) ?></span>
                                        <span><i class="fas fa-file-video mr-1"></i><?= strtoupper($videoSource['format']) ?></span>
                                    <?php endif; ?>
                                </div>
                                <!-- Tags -->
                                <div class="flex flex-wrap gap-1 mt-2">
                                    <?php
                                    $tags = $video['tags'] ? explode(',', $video['tags']) : [$video['category']];
                                    foreach (array_slice($tags, 0, 3) as $tag):
                                        $tag = trim($tag);
                                        if ($tag):
                                    ?>
                                        <span class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                                            #<?= htmlspecialchars($tag) ?>
                                        </span>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                            </div>
                        </div>


                    </div>




                    <!-- Video Counter -->
                    <div class="absolute top-16 right-6 bg-black bg-opacity-50 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full z-30 font-medium">
                        <?= $index + 1 ?> / <?= count($videos) ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bottom Navigation (Mobile Only) -->
    <div class="md:hidden bottom-nav fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 z-40">
        <div class="flex justify-around items-center py-2">
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Home</span>
            </a>
            <a href="#" class="flex flex-col items-center py-2 px-4 text-blue-500">
                <i class="fas fa-play text-xl mb-1"></i>
                <span class="text-xs">Video</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-search text-xl mb-1"></i>
                <span class="text-xs">Cari</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-bookmark text-xl mb-1"></i>
                <span class="text-xs">Simpan</span>
            </a>
        </div>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="comment-modal fixed inset-x-0 bottom-0 bg-white rounded-t-3xl z-50 max-h-96">
        <div class="p-4">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Komentar</h3>
                <button onclick="closeCommentModal()" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>

            <!-- Comments List -->
            <div id="commentsList" class="space-y-4 max-h-60 overflow-y-auto mb-4">
                <!-- Sample Comments -->
                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">User123</p>
                            <p class="text-sm text-gray-700">Video yang bagus! Sangat informatif.</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>2 jam lalu</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>12
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">NewsLover</p>
                            <p class="text-sm text-gray-700">Terima kasih atas informasinya!</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>1 jam lalu</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>5
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comment Input -->
            <div class="border-t pt-4 space-y-3">
                <!-- Name Input -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-gray-600 text-xs"></i>
                    </div>
                    <input
                        type="text"
                        placeholder="Nama Anda..."
                        class="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        id="commentName"
                        maxlength="50"
                    >
                </div>

                <!-- Comment Input -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-comment text-white text-xs"></i>
                    </div>
                    <div class="flex-1 flex items-center space-x-2">
                        <input
                            type="text"
                            placeholder="Tulis komentar..."
                            class="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            id="commentInput"
                            maxlength="1000"
                        >
                        <button
                            onclick="addComment()"
                            class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
                        >
                            <i class="fas fa-paper-plane text-white text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentVideoIndex = 0;
        let currentVideoId = null;
        const videos = <?= json_encode($videos) ?>;

        console.log('🎬 Loaded videos:', videos.length);
        console.log('📋 Videos data:', videos);

        // Check for iframe loading issues and show fallback
        function checkIframeLoading() {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                const videoId = iframe.id.replace('video-', '').replace('video-mobile-', '');

                // Set a timeout to check if iframe loaded
                setTimeout(() => {
                    try {
                        // Try to access iframe content (will fail if not loaded)
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (!iframeDoc || iframeDoc.readyState !== 'complete') {
                            console.warn('⚠️ Iframe may not have loaded properly for video:', videoId);
                        }
                    } catch (e) {
                        // This is expected for cross-origin iframes (YouTube)
                        console.log('✅ Iframe loaded (cross-origin):', videoId);
                    }
                }, 3000);
            });
        }

        // Load like and save status for all videos when page loads
        if (videos.length > 0) {
            videos.forEach(video => {
                loadLikeStatus(video.id);
                loadSaveStatus(video.id);
            });

            // Check iframe loading after a delay
            setTimeout(checkIframeLoading, 2000);
        } else {
            console.warn('⚠️ No videos found to display');
        }

        // Comment Modal Functions
        function openCommentModal(videoId) {
            currentVideoId = videoId;
            const modal = document.getElementById('commentModal');
            modal.classList.add('show');

            // Load comments for this video
            loadComments(videoId);
        }

        function closeCommentModal() {
            const modal = document.getElementById('commentModal');
            modal.classList.remove('show');
            currentVideoId = null;
        }

        function loadComments(videoId) {
            console.log('Loading comments for video:', videoId);

            fetch(`../admin/api/video_comments.php?video_id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayComments(data.comments);
                    } else {
                        console.error('Error loading comments:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error loading comments:', error);
                });
        }

        function displayComments(comments) {
            const commentsList = document.getElementById('commentsList');
            commentsList.innerHTML = '';

            if (comments.length === 0) {
                commentsList.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-comment text-3xl mb-2"></i>
                        <p>Belum ada komentar. Jadilah yang pertama!</p>
                    </div>
                `;
                return;
            }

            comments.forEach(comment => {
                const commentElement = document.createElement('div');
                commentElement.className = 'flex space-x-3';
                commentElement.innerHTML = `
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">${escapeHtml(comment.name)}</p>
                            <p class="text-sm text-gray-700">${escapeHtml(comment.comment)}</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>${comment.time_ago}</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>0
                            </button>
                        </div>
                    </div>
                `;
                commentsList.appendChild(commentElement);
            });
        }

        function addComment() {
            const nameInput = document.getElementById('commentName');
            const commentInput = document.getElementById('commentInput');
            const name = nameInput.value.trim();
            const comment = commentInput.value.trim();

            if (!comment || !currentVideoId) return;
            if (!name) {
                alert('Silakan masukkan nama Anda');
                nameInput.focus();
                return;
            }
            if (comment.length < 3) {
                alert('Komentar minimal 3 karakter');
                commentInput.focus();
                return;
            }

            // Send comment to server
            fetch('../admin/api/video_comments.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: currentVideoId,
                    name: name,
                    email: '',
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear inputs
                    nameInput.value = '';
                    commentInput.value = '';

                    // Update comment count in UI
                    updateCommentCount(currentVideoId);

                    // Reload comments to show the new one
                    loadComments(currentVideoId);

                    console.log('Comment added successfully');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error adding comment:', error);
                alert('Gagal menambahkan komentar. Silakan coba lagi.');
            });
        }

        function updateCommentCount(videoId) {
            // Update comment count in the UI
            const commentCounts = document.querySelectorAll(`[data-video-id="${videoId}"] + span`);
            commentCounts.forEach(count => {
                if (count.textContent && !isNaN(parseInt(count.textContent))) {
                    const currentCount = parseInt(count.textContent.replace(/[^\d]/g, ''));
                    count.textContent = (currentCount + 1).toLocaleString();
                }
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Show toast notification
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-full text-sm z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.3s';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 2000);
        }

        // Toggle save functionality with IP-based tracking
        async function toggleSave(videoId) {
            console.log('💾 toggleSave called for video:', videoId);
            console.log('📱 Checking if called from mobile or desktop');

            const saveBtnMobile = document.querySelector(`.save-btn[data-video-id="${videoId}"]`);

            // Disable button during request
            if (saveBtnMobile) saveBtnMobile.disabled = true;

            try {
                // Send request to toggle save
                const response = await fetch('../admin/api/video_saves.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: videoId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update UI based on server response
                    updateSaveUI(videoId, data.saved);

                    // Show feedback
                    const message = data.saved ? 'Video disimpan' : 'Video dihapus dari simpanan';
                    showToast(message);

                    console.log(`✅ Save ${data.action} successfully`);
                } else {
                    console.error('❌ Failed to toggle save:', data.error);
                    alert('Gagal memproses simpan. Silakan coba lagi.');
                }
            } catch (error) {
                console.error('❌ Error toggling save:', error);
                alert('Terjadi kesalahan. Silakan coba lagi.');
            } finally {
                // Re-enable button
                if (saveBtnMobile) saveBtnMobile.disabled = false;
            }
        }

        // Update save UI elements
        function updateSaveUI(videoId, isSaved) {
            // Update all save buttons (desktop and mobile)
            const saveButtons = document.querySelectorAll(`.save-btn[data-video-id="${videoId}"]`);

            saveButtons.forEach(saveBtn => {
                const saveIcon = saveBtn.querySelector('i');

                // Add animation
                saveBtn.classList.add('save-animation');
                setTimeout(() => saveBtn.classList.remove('save-animation'), 400);

                // Update button state for both desktop and mobile
                if (isSaved) {
                    saveBtn.classList.add('saved');
                    saveIcon.classList.remove('text-white');
                    saveIcon.classList.add('text-yellow-400');
                    console.log('💾 Save button activated (yellow)');
                } else {
                    saveBtn.classList.remove('saved');
                    saveIcon.classList.remove('text-yellow-400');
                    saveIcon.classList.add('text-white');
                    console.log('🗑️ Save button deactivated (white)');
                }
            });
        }

        // Load initial save status
        async function loadSaveStatus(videoId) {
            try {
                const response = await fetch(`../admin/api/video_saves.php?video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    updateSaveUI(videoId, data.saved);
                }
            } catch (error) {
                console.error('Error loading save status:', error);
            }
        }

        // Toggle video play/pause
        function toggleVideoPlay(videoId) {
            console.log('🎬 toggleVideoPlay called with videoId:', videoId);

            // Detect if we're on mobile or desktop
            const isMobile = window.innerWidth < 768;
            console.log('📱 Is mobile:', isMobile);

            // Get the appropriate video container
            let videoContainer;
            if (isMobile) {
                videoContainer = document.querySelector(`.md\\:hidden .video-container[data-video-id="${videoId}"]`);
            } else {
                videoContainer = document.querySelector(`.hidden.md\\:flex .video-container[data-video-id="${videoId}"]`);
            }

            // Fallback to any video container if specific one not found
            if (!videoContainer) {
                videoContainer = document.querySelector(`.video-container[data-video-id="${videoId}"]`);
            }

            if (!videoContainer) {
                console.error('❌ Video container not found for ID:', videoId);
                return;
            }

            const playButton = videoContainer.querySelector('.play-button');
            const videoType = videoContainer.getAttribute('data-video-type');
            const iframe = videoContainer.querySelector('iframe');
            const htmlVideo = videoContainer.querySelector('video');

            console.log('📦 Video container found:', videoContainer);
            console.log('▶️ Play button found:', playButton);
            console.log('🎥 Video type:', videoType);
            console.log('🎮 HTML Video element:', htmlVideo);
            console.log('🎞️ Iframe element:', iframe);
            console.log('⏯️ Current video state:', videoContainer.classList.contains('video-paused') ? 'paused' : 'playing');

            if (videoContainer.classList.contains('video-paused')) {
                // Play video
                videoContainer.classList.remove('video-paused');
                videoContainer.classList.add('video-playing');

                // Hide play button with smooth transition
                if (playButton) {
                    playButton.style.opacity = '0';
                    playButton.style.visibility = 'hidden';
                    playButton.style.pointerEvents = 'none';
                    playButton.style.transform = 'scale(0.8)';
                }

                if (videoType === 'upload' && htmlVideo) {
                    // HTML5 Video
                    try {
                        console.log('🎥 Attempting to play HTML5 video');
                        console.log('📹 Video element:', htmlVideo);
                        console.log('📊 Video readyState:', htmlVideo.readyState);
                        console.log('🔗 Video src:', htmlVideo.src ? 'Set' : 'Not set');

                        // Ensure video is loaded
                        if (htmlVideo.readyState < 2) {
                            console.log('⏳ Video not ready, loading...');
                            htmlVideo.load();
                        }

                        // Force play for mobile and desktop
                        const playPromise = htmlVideo.play();
                        if (playPromise !== undefined) {
                            playPromise.then(() => {
                                console.log(`✅ HTML5 Video ${videoId} started playing successfully`);
                                // Ensure video is visible and playing
                                htmlVideo.style.display = 'block';
                                htmlVideo.style.visibility = 'visible';
                                htmlVideo.style.opacity = '1';
                            }).catch(error => {
                                console.error('❌ Error playing HTML5 video:', error);
                                // Try to handle autoplay restrictions
                                if (error.name === 'NotAllowedError') {
                                    console.log('🔇 Autoplay blocked, user interaction required');
                                    // Show play button again
                                    if (playButton) {
                                        playButton.style.opacity = '1';
                                        playButton.style.visibility = 'visible';
                                        playButton.style.pointerEvents = 'auto';
                                    }
                                }
                            });
                        }
                    } catch (e) {
                        console.error('❌ Error playing HTML5 video:', e);
                    }
                } else if (iframe) {
                    // YouTube Video
                    try {
                        iframe.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
                        console.log(`▶️ YouTube Video ${videoId} started playing`);
                    } catch (e) {
                        console.log('⚠️ YouTube API not available');
                    }
                }

                // Don't increment view count on play button click
                // Views will be incremented on page load/refresh only
            } else {
                // Pause video
                videoContainer.classList.remove('video-playing');
                videoContainer.classList.add('video-paused');

                // Show play button with smooth transition
                if (playButton) {
                    playButton.style.opacity = '1';
                    playButton.style.visibility = 'visible';
                    playButton.style.pointerEvents = 'auto';
                    playButton.style.transform = 'scale(1)';
                }

                if (videoType === 'upload' && htmlVideo) {
                    // HTML5 Video
                    try {
                        htmlVideo.pause();
                        console.log(`⏸️ HTML5 Video ${videoId} paused`);
                    } catch (e) {
                        console.error('❌ Error pausing HTML5 video:', e);
                    }
                } else if (iframe) {
                    // YouTube Video
                    try {
                        iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                        console.log(`⏸️ YouTube Video ${videoId} paused`);
                    } catch (e) {
                        console.log('⚠️ YouTube API not available');
                    }
                }
            }
        }

        // Handle video click to increment views
        async function handleVideoClick(videoId) {
            console.log('Video clicked:', videoId);
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ View count updated');
                }
            } catch (error) {
                console.error('❌ Failed to update view count:', error);
            }
        }

        // Toggle like functionality with IP-based tracking
        async function toggleLike(videoId) {
            console.log('🔥 toggleLike called for video:', videoId);
            console.log('📱 Checking if called from mobile or desktop');

            // Find like buttons and counts
            const likeBtnMobile = document.querySelector(`.like-btn[data-video-id="${videoId}"]`);
            const likeCountMobile = document.querySelector(`.like-count[data-video-id="${videoId}"]`);

            // Disable buttons during request
            if (likeBtnMobile) likeBtnMobile.disabled = true;

            try {
                // Send request to toggle like
                const response = await fetch('../admin/api/video_likes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: videoId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Update UI based on server response
                    updateLikeUI(videoId, data.liked, data.likes_count);
                    console.log(`✅ Like ${data.action} successfully`);
                } else {
                    console.error('❌ Failed to toggle like:', data.error);
                    alert('Gagal memproses like. Silakan coba lagi.');
                }
            } catch (error) {
                console.error('❌ Error toggling like:', error);
                alert('Terjadi kesalahan. Silakan coba lagi.');
            } finally {
                // Re-enable buttons
                if (likeBtnMobile) likeBtnMobile.disabled = false;
            }
        }

        // Update like UI elements
        function updateLikeUI(videoId, isLiked, likesCount) {
            // Update all like buttons (desktop and mobile)
            const likeButtons = document.querySelectorAll(`.like-btn[data-video-id="${videoId}"]`);
            const likeCounts = document.querySelectorAll(`.like-count[data-video-id="${videoId}"]`);

            likeButtons.forEach(likeBtn => {
                const likeIcon = likeBtn.querySelector('i');

                // Add animation
                likeBtn.classList.add('like-animation');
                setTimeout(() => likeBtn.classList.remove('like-animation'), 300);

                // Update button state for both desktop and mobile
                if (isLiked) {
                    likeBtn.classList.add('liked');
                    likeIcon.classList.remove('text-white');
                    likeIcon.classList.add('text-red-500');
                    console.log('✅ Like button activated (red)');
                } else {
                    likeBtn.classList.remove('liked');
                    likeIcon.classList.remove('text-red-500');
                    likeIcon.classList.add('text-white');
                    console.log('❌ Like button deactivated (white)');
                }
            });

            // Update like counts
            likeCounts.forEach(likeCount => {
                likeCount.textContent = likesCount.toLocaleString();
            });
        }

        // Load initial like status when page loads
        async function loadLikeStatus(videoId) {
            try {
                const response = await fetch(`../admin/api/video_likes.php?video_id=${videoId}`);
                const data = await response.json();

                if (data.success) {
                    updateLikeUI(videoId, data.liked, data.likes_count);
                }
            } catch (error) {
                console.error('Error loading like status:', error);
            }
        }



        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('commentModal');
            if (e.target === modal) {
                closeCommentModal();
            }
        });

        // Handle Enter key in comment input
        document.getElementById('commentInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addComment();
            }
        });

        // Share video functionality
        async function shareVideo(videoId) {
            console.log('Sharing video:', videoId);

            const shareCountMobile = document.querySelector(`.share-count[data-video-id="${videoId}"]`);
            const shareCountDesktop = document.querySelector(`.share-count-desktop[data-video-id="${videoId}"]`);

            let currentCount = 0;
            if (shareCountMobile) {
                currentCount = parseInt(shareCountMobile.textContent.replace(/,/g, ''));
            } else if (shareCountDesktop) {
                currentCount = parseInt(shareCountDesktop.textContent.replace(/,/g, ''));
            }
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Share count updated');
                    const newCount = (currentCount + 1).toLocaleString();
                    if (shareCountMobile) shareCountMobile.textContent = newCount;
                    if (shareCountDesktop) shareCountDesktop.textContent = newCount;
                    
                    // Show share options
                    const video = videos.find(v => v.id == videoId);
                    if (navigator.share) {
                        navigator.share({
                            title: video.title,
                            text: video.description,
                            url: video.youtube_url
                        });
                    } else {
                        // Fallback: copy to clipboard
                        navigator.clipboard.writeText(video.youtube_url);
                        alert('Link video telah disalin ke clipboard!');
                    }
                }
            } catch (error) {
                console.error('❌ Failed to update share count:', error);
            }
        }

        // Auto-increment view count when video becomes visible
        function incrementViewOnVisible() {
            const videoItems = document.querySelectorAll('.video-item');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const videoId = entry.target.dataset.videoId;
                        if (videoId) {
                            setTimeout(() => handleVideoClick(videoId), 1000);
                        }
                    }
                });
            }, { threshold: 0.5 });

            videoItems.forEach(item => observer.observe(item));
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('commentModal');
            if (e.target === modal) {
                closeCommentModal();
            }
        });

        // Listen for YouTube player state changes
        window.addEventListener('message', function(event) {
            if (event.origin !== 'https://www.youtube.com') return;

            try {
                const data = JSON.parse(event.data);
                if (data.info && data.info.playerState !== undefined) {
                    const videoId = <?= $video['id'] ?>;
                    const videoContainer = document.querySelector(`.video-container[data-video-id="${videoId}"]`);
                    const playButton = videoContainer.querySelector('.play-button');

                    // YouTube player states: -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)
                    if (data.info.playerState === 1) {
                        // Video is playing
                        videoContainer.classList.remove('video-paused');
                        videoContainer.classList.add('video-playing');
                        playButton.style.opacity = '0';
                        playButton.style.visibility = 'hidden';
                        playButton.style.pointerEvents = 'none';
                        playButton.style.transform = 'scale(0.8)';
                    } else if (data.info.playerState === 2 || data.info.playerState === 0) {
                        // Video is paused or ended
                        videoContainer.classList.remove('video-playing');
                        videoContainer.classList.add('video-paused');
                        playButton.style.opacity = '1';
                        playButton.style.visibility = 'visible';
                        playButton.style.pointerEvents = 'auto';
                        playButton.style.transform = 'scale(1)';
                    }
                }
            } catch (e) {
                // Ignore parsing errors
            }
        });

        // Handle mobile video click
        function handleMobileVideoClick(videoElement, videoId) {
            console.log('📱 Mobile video clicked:', videoId);

            // Force video to play/pause
            if (videoElement.paused) {
                videoElement.play().then(() => {
                    console.log('✅ Mobile video started playing');
                }).catch(error => {
                    console.error('❌ Error playing mobile video:', error);
                });
            } else {
                videoElement.pause();
                console.log('⏸️ Mobile video paused');
            }
        }

        // Handle video loading errors
        function handleVideoError(videoElement, videoId) {
            console.error('Video failed to load:', videoId);

            // Hide video element and show error placeholder
            videoElement.style.display = 'none';

            // Check if it's mobile or desktop video
            const isMobile = videoElement.id.includes('mobile');
            const errorDivId = isMobile ? `video-error-mobile-${videoId}` : `video-error-${videoId}`;
            const errorDiv = document.getElementById(errorDivId);

            if (errorDiv) {
                errorDiv.classList.remove('hidden');
            }

            // Also hide play button for failed videos
            const videoContainer = videoElement.closest('.video-container');
            if (videoContainer) {
                const playButton = videoContainer.querySelector('.play-button');
                if (playButton) {
                    playButton.style.display = 'none';
                }
            }

            console.log(`Video error handled for ${isMobile ? 'mobile' : 'desktop'} video ${videoId}`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Video page initialized');
            console.log('📱 Is mobile:', window.innerWidth < 768);
            console.log('🎥 Total videos:', videos.length);
            console.log('🔍 Page URL:', window.location.href);
            console.log('📊 Videos data:', videos);

            // Check if videos are actually rendered in DOM
            const videoElements = document.querySelectorAll('.video-item');
            console.log('🎬 Video elements found in DOM:', videoElements.length);

            const iframes = document.querySelectorAll('iframe');
            console.log('📺 Iframe elements found:', iframes.length);

            if (iframes.length > 0) {
                iframes.forEach((iframe, index) => {
                    console.log(`📺 Iframe ${index + 1} src:`, iframe.src);
                });
            }

            incrementViewOnVisible();

            // Load initial states for all videos
            videos.forEach(video => {
                loadLikeStatus(video.id);
                loadSaveStatus(video.id);
            });

            // Add click event listeners to play buttons (both desktop and mobile)
            const playButtons = document.querySelectorAll('.play-button, .play-button button');
            console.log('Found play buttons:', playButtons.length);

            playButtons.forEach((button, index) => {
                console.log(`Setting up play button ${index}:`, button);
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Play button clicked directly');
                    const videoId = <?= $video['id'] ?>;
                    console.log('Calling toggleVideoPlay with videoId:', videoId);
                    toggleVideoPlay(videoId);
                });

                // Add touch events for better mobile interaction
                button.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    this.style.opacity = '0.8';
                });

                button.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                });
            });

            // Add click event listeners to video containers for play/pause
            const videoContainers = document.querySelectorAll('.video-container');
            console.log('Found video containers:', videoContainers.length);

            videoContainers.forEach((container, index) => {
                console.log(`Setting up video container ${index}:`, container);
                const videoId = container.getAttribute('data-video-id');

                container.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.action-btn') || e.target.closest('.like-btn') || e.target.closest('.save-btn') || e.target.closest('.share-btn')) {
                        return;
                    }

                    console.log('Video container clicked, videoId:', videoId);
                    toggleVideoPlay(videoId);
                });
            });

            // Add event listeners for mobile action buttons
            const mobileActionButtons = document.querySelectorAll('.md\\:hidden .action-btn');
            mobileActionButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Mobile action button clicked:', this.className);
                });

                // Add touch events for better mobile interaction
                button.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.95)';
                });

                button.addEventListener('touchend', function(e) {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add event listeners for HTML5 videos
            const htmlVideos = document.querySelectorAll('video[data-video-id]');
            htmlVideos.forEach(video => {
                const videoId = video.getAttribute('data-video-id');
                const isMobileVideo = video.id.includes('mobile');

                video.addEventListener('play', function() {
                    console.log(`▶️ HTML5 video ${videoId} started playing (${isMobileVideo ? 'mobile' : 'desktop'})`);
                    // Don't increment view count on play - only on page load
                });

                video.addEventListener('pause', function() {
                    console.log(`⏸️ HTML5 video ${videoId} paused (${isMobileVideo ? 'mobile' : 'desktop'})`);
                });

                video.addEventListener('ended', function() {
                    console.log(`🏁 HTML5 video ${videoId} ended (${isMobileVideo ? 'mobile' : 'desktop'})`);
                });

                video.addEventListener('loadedmetadata', function() {
                    console.log(`📊 HTML5 video ${videoId} metadata loaded (${isMobileVideo ? 'mobile' : 'desktop'})`);

                    // For mobile videos, ensure proper sizing
                    if (isMobileVideo) {
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'contain';
                    }
                });

                video.addEventListener('canplay', function() {
                    console.log(`✅ HTML5 video ${videoId} can play (${isMobileVideo ? 'mobile' : 'desktop'})`);
                });

                video.addEventListener('error', function(e) {
                    console.error(`❌ HTML5 video ${videoId} error (${isMobileVideo ? 'mobile' : 'desktop'}):`, e);
                });

                // Add mobile-specific touch events
                if (isMobileVideo) {
                    video.addEventListener('touchstart', function(e) {
                        console.log('📱 Mobile video touched');
                    });

                    video.addEventListener('click', function(e) {
                        console.log('📱 Mobile video clicked');
                        e.stopPropagation();
                    });
                }
            });

            // Handle Enter key in comment input
            const commentInput = document.getElementById('commentInput');
            if (commentInput) {
                commentInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        addComment();
                    }
                });
            }
        });
    </script>
</body>
</html>
